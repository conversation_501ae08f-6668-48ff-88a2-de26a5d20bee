defmodule RepobotWeb.Live.Onboarding.Steps.TemplateFiles do
  use RepobotWeb.Live.Onboarding.Step

  alias Repobot.Files
  alias Repobot.Repositories
  alias Repobot.Workers.RepositoryFilesWorker
  require Logger

  @impl true
  def update(%{repository_files_progress: {progress, message}} = assigns, socket) do
    # Handle repository files loading progress updates
    {:ok,
     socket
     |> assign(assigns)
     |> assign(:loading_progress, progress)
     |> assign(:loading_message, message)}
  end

  def update(%{repository_files_complete: result} = assigns, socket) do
    # Handle repository files loading completion
    case result do
      %{"status" => "ok", "operation" => "load_trees", "repository_ids" => _repository_ids} ->
        Logger.info("Repository trees loaded successfully, finding common files...")

        socket =
          socket
          |> assign(assigns)
          |> assign(:loading_progress, 100)
          |> assign(:loading_message, "Trees loaded, finding common files...")

        # Find common files and start content refresh
        updated_socket = find_common_files_and_refresh_content(socket)
        {:ok, updated_socket}

      %{
        "status" => "ok",
        "operation" => "refresh_content",
        "repository_ids" => repository_ids
      } ->
        # Content refresh is complete. Reload repositories with fresh content.
        refreshed_repos = Repositories.list_repositories_by_ids(repository_ids)

        refreshed_repos =
          Enum.map(refreshed_repos, &Repobot.Repo.preload(&1, :files, force: true))

        Logger.debug(
          "[TemplateFiles:content_refresh_complete] Received #{length(refreshed_repos)} refreshed repos."
        )

        # Log content status for a sample file (e.g., LICENSE) if present
        Enum.each(refreshed_repos, fn repo ->
          license_file = Enum.find(repo.files, &(&1.path == "LICENSE"))

          if license_file do
            Logger.debug(
              "[TemplateFiles:content_refresh_complete] Repo #{repo.id} LICENSE content size: #{byte_size(license_file.content || "")}"
            )
          else
            Logger.debug(
              "[TemplateFiles:content_refresh_complete] Repo #{repo.id} does not have LICENSE."
            )
          end
        end)

        socket =
          socket
          |> assign(assigns)
          |> assign(:content_refresh_progress, 100)
          |> assign(:refreshed_repos, refreshed_repos)
          |> assign(:similarity_progress, 0)
          |> assign(:loading_common_files, true)

        # Explicitly get common_files from assigns and ensure it's a list
        common_files = socket.assigns.common_files || []

        # Calculate similarity using the previously found common_files paths/info
        # and the newly refreshed repository data.
        Logger.info(
          "Content refresh complete. Starting similarity calculation for #{length(common_files)} common files."
        )

        Files.calculate_common_files_similarity(
          common_files,
          refreshed_repos,
          {self(), :template_files}
        )

        {:ok, socket}

      %{"status" => "error", "reason" => reason} ->
        Logger.error("Repository files loading failed: #{inspect(reason)}")

        {:ok,
         socket
         |> assign(assigns)
         |> assign(:loading_common_files, false)
         |> assign(:common_files_error, "Failed to load repository files: #{reason}")}

      # Handle legacy format for backward compatibility
      {:ok, refreshed_repos} when is_list(refreshed_repos) ->
        socket =
          socket
          |> assign(assigns)
          |> assign(:content_refresh_progress, 100)
          |> assign(:refreshed_repos, refreshed_repos)
          |> assign(:similarity_progress, 0)

        common_files = socket.assigns.common_files || []

        Files.calculate_common_files_similarity(
          common_files,
          refreshed_repos,
          {self(), :template_files}
        )

        {:ok, socket}

      {:error, reason} ->
        Logger.error("Repository files loading failed: #{inspect(reason)}")

        {:ok,
         socket
         |> assign(assigns)
         |> assign(:loading_common_files, false)
         |> assign(:common_files_error, "Failed to load repository files: #{reason}")}
    end
  end

  def update(%{files_message: message}, socket) do
    case message do
      {:similarity_progress, progress} ->
        {:ok, assign(socket, :similarity_progress, trunc(progress))}

      {:similarity_complete, files} ->
        Logger.debug(
          "[TemplateFiles:similarity_complete] Received #{length(files)} files after similarity calc."
        )

        # Log content status for a sample file (e.g., LICENSE) if present
        license_file_data = Enum.find(files, &(&1["path"] == "LICENSE"))

        if license_file_data do
          Logger.debug(
            "[TemplateFiles:similarity_complete] LICENSE data content size: #{byte_size(license_file_data["content"] || "")}"
          )
        else
          Logger.debug(
            "[TemplateFiles:similarity_complete] LICENSE not found in similarity results."
          )
        end

        {:ok,
         socket
         |> assign(:common_files, files)
         |> assign(:loading_common_files, false)}
    end
  end

  def update(%{finalize_step: true}, socket) do
    # Handle finalization request from parent component
    selected_template_files = socket.assigns.selected_template_files

    # Prepare selected files with content from common files
    selected_files =
      selected_template_files
      |> Enum.map(fn file_path ->
        file = Enum.find(socket.assigns.common_files, &(&1["path"] == file_path))

        source_repo =
          Enum.find(socket.assigns.state.selected_repos, fn repo ->
            Enum.any?(repo.files, &(&1.path == file_path))
          end)

        %{
          path: file_path,
          content: file["content"],
          source_repo: source_repo
        }
      end)

    # Finalize the step with the selected template files and prepared files
    finalize(:template_files, %{
      selected_template_files: selected_template_files,
      selected_files: selected_files
    })

    {:ok, socket}
  end

  def update(%{repository_files_updated: {repository_id, _metadata}}, socket) do
    # Check if the updated repository is one of our repositories
    state = socket.assigns.state

    all_repo_ids =
      ([state.template_repo] ++ state.selected_repos)
      |> Enum.map(& &1.id)
      |> MapSet.new()

    if MapSet.member?(all_repo_ids, repository_id) do
      # One of our repositories was updated, refresh common files
      Logger.info(
        "Repository #{repository_id} files updated, refreshing common files in onboarding"
      )

      socket = assign(socket, :loading_common_files, true)
      {:ok, maybe_initialize_files(socket)}
    else
      {:ok, socket}
    end
  end

  def update(assigns, socket) do
    socket =
      socket
      |> assign(assigns)
      |> assign_new(:loading_states, fn -> %{} end)
      |> assign_new(:common_files, fn -> [] end)
      |> assign_new(:loading_common_files, fn -> false end)
      |> assign_new(:common_files_error, fn -> nil end)
      |> assign_new(:content_refresh_progress, fn -> 0 end)
      |> assign_new(:similarity_progress, fn -> 0 end)
      |> assign_new(:loading_progress, fn -> 0 end)
      |> assign_new(:loading_message, fn -> "" end)
      |> assign_new(:excluded_existing_files, fn -> [] end)
      |> assign_new(:selected_template_files, fn -> MapSet.new() end)
      |> maybe_load_repositories()
      |> maybe_initialize_files()

    {:ok, socket}
  end

  @doc """
  Renders the template files step of the onboarding process.
  """
  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <h2 class="text-2xl font-semibold text-slate-900 mb-4">Template Files</h2>
      <%= if @loading_common_files do %>
        <div class="flex flex-col items-center justify-center py-12">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
          <p class="mt-4 text-slate-600">
            Loading common files...
          </p>

          <%= if @loading_progress && @loading_progress > 0 do %>
            <div class="w-full max-w-lg mt-4">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm text-slate-600">{@loading_message || "Loading..."}</span>
                <span class="text-sm text-slate-600">{@loading_progress}%</span>
              </div>
              <div class="w-full bg-slate-200 rounded-full h-2">
                <div
                  class="bg-indigo-600 h-2 rounded-full transition-all duration-200"
                  style={"width: #{@loading_progress}%"}
                >
                </div>
              </div>
            </div>
          <% end %>

          <%= if @content_refresh_progress && @content_refresh_progress > 0 do %>
            <div class="w-full max-w-lg mt-4">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm text-slate-600">Refreshing file content...</span>
                <span class="text-sm text-slate-600">{@content_refresh_progress}%</span>
              </div>
              <div class="w-full bg-slate-200 rounded-full h-2">
                <div
                  class="bg-indigo-600 h-2 rounded-full transition-all duration-200"
                  style={"width: #{@content_refresh_progress}%"}
                >
                </div>
              </div>
            </div>
          <% end %>

          <%= if @similarity_progress && @similarity_progress > 0 do %>
            <div class="w-full max-w-lg mt-4">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm text-slate-600">Calculating file similarities...</span>
                <span class="text-sm text-slate-600">{@similarity_progress}%</span>
              </div>
              <div class="w-full bg-slate-200 rounded-full h-2">
                <div
                  class="bg-indigo-600 h-2 rounded-full transition-all duration-200"
                  style={"width: #{@similarity_progress}%"}
                >
                </div>
              </div>
            </div>
          <% end %>
        </div>
      <% else %>
        <div>
          <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <h3 class="text-sm font-medium text-blue-800 mb-2">Setup Summary</h3>
            <ul class="text-sm text-blue-700 space-y-1">
              <li class="flex items-center gap-2">
                <.icon name="hero-check-circle" class="w-4 h-4 text-blue-600" /> Template repository:
                <span class="font-medium">
                  {@state.template_repo.full_name}
                </span>
              </li>
              <li class="flex items-center gap-2">
                <.icon name="hero-check-circle" class="w-4 h-4 text-blue-600" /> Target repositories:
                <span class="font-medium">{length(@state.selected_repos)}</span>
                selected
              </li>
              <%= if @state[:target_folders] && Enum.any?(@state.target_folders) do %>
                <li class="flex items-center gap-2">
                  <.icon name="hero-check-circle" class="w-4 h-4 text-blue-600" /> Folders:
                  <span class="font-medium">
                    {Enum.map_join(@state.target_folders, ", ", & &1.name)}
                  </span>
                </li>
              <% end %>
            </ul>
          </div>

          <p class="text-slate-600 mb-6">
            Select the files you want to include in the template repository. These files will be:
          </p>
          <ul class="list-disc list-inside text-sm text-slate-600 mb-6 space-y-1">
            <li>Pushed to the template repository on GitHub</li>
            <li>Imported as source files for the template</li>
            <li>Used to sync changes across all target repositories</li>
          </ul>

          <div class="bg-amber-50 border border-amber-200 rounded-lg p-4 mb-6">
            <div class="flex items-start gap-2">
              <.icon
                name="hero-information-circle"
                class="w-5 h-5 text-amber-600 mt-0.5 flex-shrink-0"
              />
              <p class="text-sm text-amber-800">
                Only files that are identical (100% similar) across selected repositories are shown.
                These files will be added to the template repository
                <%= if @state.template_repo && not @state.template_repo.template do %>
                  and the repository will be marked as a template.
                <% end %>
              </p>
            </div>
          </div>

          <%= if @excluded_existing_files && Enum.any?(@excluded_existing_files) do %>
            <div class="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div class="flex items-start gap-3">
                <.icon
                  name="hero-exclamation-triangle"
                  class="w-5 h-5 text-yellow-500 mt-0.5 flex-shrink-0"
                />
                <div>
                  <h3 class="text-sm font-medium text-yellow-800">
                    Files Already in Template Repository ({length(@excluded_existing_files)})
                  </h3>
                  <p class="mt-1 text-sm text-yellow-700">
                    The following files were found to be common across your selected target repositories but are already present in the template repository (<code class="text-xs"><%= @state.template_repo.full_name %></code>) and cannot be selected for onboarding:
                  </p>
                  <ul class="mt-2 list-disc list-inside space-y-1">
                    <%= for file <- @excluded_existing_files do %>
                      <li class="text-sm text-yellow-700">
                        <code class="text-xs font-mono bg-yellow-100 px-1 py-0.5 rounded">
                          {file["path"]}
                        </code>
                      </li>
                    <% end %>
                  </ul>
                </div>
              </div>
            </div>
          <% end %>

          <%= if @common_files_error do %>
            <div class="mb-4 p-4 bg-red-50 rounded-md">
              <p class="text-red-700">
                {@common_files_error}
              </p>
            </div>
          <% end %>

          <div class="space-y-6">
            <%= if Enum.any?(@common_files) do %>
              <ul aria-labelledby="common-files-heading" data-testid="common-files-list">
                <%= for file <- @common_files do %>
                  <li class="flex items-center gap-4">
                    <input
                      type="checkbox"
                      id={"selected_template_files_#{file["path"]}"}
                      name="selected_template_files"
                      value={file["path"]}
                      checked={MapSet.member?(@selected_template_files, file["path"])}
                      phx-click="toggle_file"
                      phx-value-path={file["path"]}
                      phx-target={@myself}
                      class="h-4 w-4 border-slate-300 rounded text-indigo-600 focus:ring-indigo-600"
                    />
                    <label
                      for={"selected_template_files_#{file["path"]}"}
                      class="text-sm text-slate-900"
                    >
                      <span class="font-mono font-medium" data-testid="common-file-name">
                        {file["path"]}
                      </span>
                      <span class="ml-2 text-xs text-slate-500">
                        ({file["count"]} of {file["total_repos"]} repositories)
                      </span>
                    </label>
                  </li>
                <% end %>
              </ul>
            <% end %>

            <%= if Enum.empty?(@common_files) do %>
              <div class="text-sm text-slate-500 italic" data-testid="no-common-files-message">
                No identical files found across selected repositories.
              </div>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
    """
  end

  # Event handlers
  @impl true
  def handle_event("toggle_file", %{"path" => path}, socket) do
    selected_template_files =
      if MapSet.member?(socket.assigns.selected_template_files, path) do
        MapSet.delete(socket.assigns.selected_template_files, path)
      else
        MapSet.put(socket.assigns.selected_template_files, path)
      end

    socket = assign(socket, :selected_template_files, selected_template_files)

    # Don't finalize on every toggle - just update the local state
    # The step will be finalized when the user actually proceeds to the next step
    {:noreply, socket}
  end

  def finalize(socket) do
    selected_paths = socket.assigns.selected_template_files
    # Still needed for source_repo info
    refreshed_repos = socket.assigns.refreshed_repos

    if is_nil(refreshed_repos) do
      Logger.error("Attempted to finalize TemplateFiles step but refreshed_repos is nil.")
      {:ok, %{selected_files: []}}
    else
      selected_files_with_content =
        Enum.reduce(selected_paths, [], fn path, acc ->
          # Find the first repo and file struct matching the path in the potentially stale refreshed_repos list
          # We primarily need this to find the source_repo ID and confirm existence.
          found_repo_and_file =
            Enum.find_value(refreshed_repos, fn repo ->
              case Enum.find(repo.files, &(&1.path == path)) do
                nil -> nil
                # Return {repo, file}
                file_struct -> {repo, file_struct}
              end
            end)

          if found_repo_and_file do
            {source_repo, _stale_file_struct} = found_repo_and_file

            # Fetch the UP-TO-DATE content directly from the database
            db_file = Repobot.RepositoryFiles.get_repository_file_by_path(source_repo.id, path)

            # Log the fetched DB file content
            Logger.debug(
              "[TemplateFiles:finalize] Fetched DB file for path '#{path}' from repo #{source_repo.id}. Content size: #{byte_size((db_file && db_file.content) || "")}"
            )

            if db_file && db_file.content do
              # Construct the map needed for the Summary step using FRESH content
              file_data = %{
                path: path,
                # Use content from DB query
                content: db_file.content,
                # Use repo info from initial find
                source_repo: source_repo
              }

              [file_data | acc]
            else
              Logger.warning(
                "File path '#{path}' found in repo #{source_repo.id}, but DB content is missing or nil during finalize."
              )

              acc
            end
          else
            Logger.warning(
              "Selected file path '#{path}' not found in refreshed repository data during finalize."
            )

            acc
          end
        end)

      Logger.info(
        "Finalizing TemplateFiles step with #{length(selected_files_with_content)} selected files with content."
      )

      # Logger.debug("Selected files for summary: #{inspect(selected_files_with_content)}")

      {:ok, %{selected_files: selected_files_with_content}}
    end
  end

  defp maybe_initialize_files(%{assigns: %{state: state}} = socket) do
    if connected?(socket) do
      # Check if we already have common files loaded to avoid unnecessary re-initialization
      if Enum.any?(socket.assigns.common_files) and not socket.assigns.loading_common_files do
        Logger.debug("Files already loaded, skipping re-initialization")
        socket
      else
        template_repo = state.template_repo
        selected_repos = state.selected_repos

        all_repos = [template_repo | selected_repos]

        {_loading_states, repos_needing_refresh} = Files.RepoTree.init_loading(all_repos)

        Logger.debug(
          "Loading files for #{inspect(Enum.map(repos_needing_refresh, & &1.full_name))}"
        )

        if Enum.empty?(repos_needing_refresh) do
          assign_common_files(socket)
        else
          # Use Oban worker for loading trees
          start_tree_loading(socket, repos_needing_refresh)
        end
      end
    else
      socket
    end
  end

  defp start_tree_loading(socket, repositories) do
    user_id = socket.assigns.current_user.id
    repository_ids = Enum.map(repositories, & &1.id)
    topic = "repository_files:#{user_id}"

    # Subscribe to repository files loading events via Oban.Notifier
    channel = String.to_atom(topic)
    Oban.Notifier.listen([channel])

    case RepositoryFilesWorker.enqueue_tree_loading(user_id, repository_ids, topic) do
      {:ok, _job} ->
        socket
        |> assign(:loading_common_files, true)
        |> assign(:loading_progress, 0)
        |> assign(:loading_message, "Starting repository tree loading...")

      {:error, reason} ->
        # Fall back to synchronous loading on error
        Logger.warning("Failed to enqueue tree loading job: #{inspect(reason)}")

        Files.RepoTree.load_trees(
          repositories,
          {self(), :template_files},
          socket.assigns.current_user
        )

        socket
        |> assign(:loading_common_files, true)
        |> assign(:loading_progress, 0)
    end
  end

  defp start_content_refresh(socket) do
    template_repo = socket.assigns.state.template_repo
    selected_repos = socket.assigns.state.selected_repos
    all_repos = [template_repo | selected_repos]

    user_id = socket.assigns.current_user.id
    repository_ids = Enum.map(all_repos, & &1.id)
    topic = "repository_files:#{user_id}"

    case RepositoryFilesWorker.enqueue_content_refresh(user_id, repository_ids, topic) do
      {:ok, _job} ->
        {:ok, socket}

      {:error, reason} ->
        Logger.error("Failed to enqueue content refresh job: #{inspect(reason)}")

        {:ok,
         socket
         |> assign(:loading_common_files, false)
         |> assign(:common_files_error, "Failed to start content refresh: #{reason}")}
    end
  end

  defp find_common_files_and_refresh_content(socket) do
    Logger.info("All repo trees loaded, finding common files across target repos...")

    template_repo = socket.assigns.state.template_repo
    # Use ONLY selected target repos for finding common files initially
    selected_target_repos = socket.assigns.state.selected_repos

    # Find common files based on paths in target repos
    case Repobot.Files.find_common_files(selected_target_repos) do
      {:ok, common_files_in_targets} ->
        Logger.info(
          "Found #{length(common_files_in_targets)} common files based on path in target repos."
        )

        # Get paths already existing in the template repository
        # Ensure template repo files are loaded (might need explicit preload if not guaranteed)
        template_repo_preloaded = Repobot.Repo.preload(template_repo, :files)
        template_repo_paths = Enum.map(template_repo_preloaded.files, & &1.path) |> MapSet.new()
        Logger.debug("Paths already in template repo: #{inspect(template_repo_paths)}")

        # Split the common files into those addable and those already existing in the template
        {addable_common_files, excluded_existing_files} =
          Enum.split_with(common_files_in_targets, fn file ->
            is_new_to_template = not MapSet.member?(template_repo_paths, file["path"])

            if not is_new_to_template do
              Logger.info("Excluding file already in template: #{file["path"]}")
            end

            is_new_to_template
          end)

        Logger.info("#{length(addable_common_files)} common files are new to the template repo.")

        # Store the filtered list (addable files) and the excluded list
        socket =
          socket
          # Only assign addable files
          |> assign(:common_files, addable_common_files)
          # Store excluded for notification
          |> assign(:excluded_existing_files, excluded_existing_files)
          |> assign(:content_refresh_progress, 0)
          |> assign(:refreshed_repos, nil)

        # Start content refresh using Oban worker
        Logger.info("Starting content refresh...")

        case start_content_refresh(socket) do
          {:ok, updated_socket} -> updated_socket
          updated_socket -> updated_socket
        end

      {:error, reason} ->
        Logger.error("Failed to find common files in target repos: #{inspect(reason)}")

        socket
        |> assign(:common_files_error, "Failed to analyze common files: #{reason}")
        |> assign(:loading_common_files, false)
    end
  end

  defp assign_common_files(socket) do
    # This is kept for backward compatibility but now uses the new function
    case find_common_files_and_refresh_content(socket) do
      {:ok, updated_socket} -> updated_socket
      updated_socket -> updated_socket
    end
  end
end
